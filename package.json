{"name": "bookr-mobile", "version": "6.0.0", "private": true, "packageManager": "yarn@3.2.0", "scripts": {"start": "expo start", "start:local": "cross-env EXPO_PUBLIC_APP_ENV=local expo start", "start:staging": "cross-env EXPO_PUBLIC_APP_ENV=staging expo start", "start:production": "cross-env EXPO_PUBLIC_APP_ENV=production expo start", "android": "expo run:android", "ios": "expo run:ios", "ios:local": "cross-env EXPO_PUBLIC_APP_ENV=local yarn ios", "ios:staging": "cross-env EXPO_PUBLIC_APP_ENV=staging yarn ios", "ios:production": "cross-env EXPO_PUBLIC_APP_ENV=production yarn ios", "android:staging": "cross-env EXPO_PUBLIC_APP_ENV=staging yarn android", "android:production": "cross-env EXPO_PUBLIC_APP_ENV=production yarn android", "upload:sourcemap": "npx sentry-expo-upload-sourcemaps dist", "storybook": "start-storybook -p 7007", "build-storybook": "build-storybook", "format": "prettier --write .", "format:check": "prettier --check .", "tsc:check": "tsc --noEmit", "test": "jest", "test:changed": "jest --watch --coverage=false --changedSince=origin/main", "test:debug": "jest -o --watch --coverage=false", "test:snapshots": "jest -u --coverage=false", "lint": "eslint --cache --ext \".ts,.tsx\" ./src", "lint:fix": "$npm_execpath lint --fix", "postinstall": "husky install", "maker": "ts-node --project tsconfig.cli.json scripts/maker/index.ts", "release:staging": "EXPO_PUBLIC_APP_ENV=staging eas update --branch staging", "release:production-rc": "EXPO_PUBLIC_APP_ENV=production eas update --branch production-rc", "release:production": "EXPO_PUBLIC_APP_ENV=production eas update --branch production", "compress:assets": "node scripts/compress.js", "i18n:keys": "node scripts/generateTrans.js", "i18n:sort": "jscodeshift -t scripts/sortTrans.js ./src/lib/translations/resources/*.ts", "version:new": "scripty", "watchman:recrawl": "watchman watch-del './' && watchman watch-project './'"}, "dependencies": {"@bookr-technologies/api": "^0.9.33", "@bookr-technologies/core": "^0.9.33", "@bookr-technologies/env": "^0.9.33", "@bookr-technologies/logger": "^0.2.0", "@bookr-technologies/react-native-calendar": "^1.4.0", "@expo/vector-icons": "^14.0.2", "@georstat/react-native-image-gallery": "^1.1.0", "@gorhom/bottom-sheet": "^4", "@invertase/react-native-apple-authentication": "^2.2.2", "@miblanchard/react-native-slider": "^2.6.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.0.1", "@react-native-community/netinfo": "11.3.1", "@react-native-community/slider": "4.5.2", "@react-native-firebase/analytics": "^20.3.0", "@react-native-firebase/app": "^20.3.0", "@react-native-firebase/auth": "^20.3.0", "@react-native-firebase/crashlytics": "^20.3.0", "@react-native-firebase/perf": "^20.3.0", "@react-native-firebase/remote-config": "^20.3.0", "@react-native-google-signin/google-signin": "^12.2.1", "@react-navigation/bottom-tabs": "^6.4.1", "@react-navigation/native": "^6.0.14", "@react-navigation/native-stack": "^6.9.2", "@sentry/react-native": "~5.22.0", "expo": "^51.0.0", "expo-blur": "~13.0.2", "expo-build-properties": "~0.12.3", "expo-calendar": "~13.0.5", "expo-clipboard": "~6.0.3", "expo-contacts": "~13.0.4", "expo-dev-client": "~4.0.20", "expo-device": "~6.0.2", "expo-document-picker": "~12.0.2", "expo-file-system": "~17.0.1", "expo-image-picker": "~15.0.7", "expo-linking": "~6.3.1", "expo-localization": "~15.0.3", "expo-location": "~17.0.1", "expo-notifications": "~0.28.10", "expo-sharing": "~12.0.1", "expo-splash-screen": "~0.27.5", "expo-status-bar": "~1.12.1", "expo-store-review": "~7.0.2", "expo-tracking-transparency": "~4.0.2", "expo-updates": "~0.25.20", "expo-web-browser": "~13.0.3", "firebase": "^10.7.1", "formik": "^2.2.9", "google-libphonenumber": "^3.2.31", "haversine-distance": "^1.2.1", "i18next": "^22.0.6", "intl": "^1.2.5", "jest": "^29.2.1", "jest-expo": "~51.0.3", "lodash": "^4.17.21", "lottie-react-native": "6.7.0", "mixpanel-react-native": "^3.0.5", "moment-timezone": "^0.5.39", "polished": "^4.2.2", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^12.0.0", "react-native": "0.74.3", "react-native-anchor-point": "^1.0.6", "react-native-calendars": "1.1306.0", "react-native-draggable-flatlist": "^4.0.0", "react-native-expo-image-cache": "^4.1.0", "react-native-fbsdk-next": "^11.1.0", "react-native-gesture-handler": "~2.16.1", "react-native-google-places-autocomplete": "^2.5.1", "react-native-maps": "1.14.0", "react-native-pager-view": "6.3.0", "react-native-progress": "^5.0.0", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-segmented-picker": "^2.0.2", "react-native-svg": "15.2.0", "react-native-swiper": "^1.6.0", "react-native-web": "~0.19.6", "react-native-webview": "13.8.6", "react-query": "^3.39.2", "styled-components": "^5.3.6", "victory-native": "^36.6.8", "yup": "^0.32.11", "zustand": "^4.1.4"}, "devDependencies": {"@babel/core": "^7.19.3", "@expo/metro-config": "~0.18.1", "@storybook/addon-actions": "^6.5", "@storybook/addon-knobs": "^5.3", "@storybook/addon-links": "^6.5", "@storybook/addon-ondevice-actions": "^5.3.23", "@storybook/addon-ondevice-controls": "^6.0.1-alpha.0", "@storybook/addon-ondevice-knobs": "^5.3.26", "@storybook/react-native": "^5.3.27", "@storybook/react-native-server": "^5.3.23", "@trivago/prettier-plugin-sort-imports": "^4.0.0", "@types/google-libphonenumber": "^7.4.23", "@types/jest": "^29.2.3", "@types/jscodeshift": "^0.11.5", "@types/lodash": "^4.14.190", "@types/react": "~18.2.14", "@types/styled-components-react-native": "^5.2.0", "@typescript-eslint/eslint-plugin": "^5.44.0", "@typescript-eslint/parser": "^5.44.0", "babel-loader": "^9.1.0", "babel-plugin-module-resolver": "^4.1.0", "chalk": "^4.1.2", "cross-env": "^7.0.3", "eslint": "^8.28.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-typescript-sort-keys": "^2.1.0", "generate-changelog": "^1.8.0", "glob": "^8.0.3", "husky": "^8.0.2", "jscodeshift": "^0.13.1", "lint-staged": ">=13", "metro-resolver": "^0.80.9", "prettier": "^2.8.0", "react-test-renderer": "18", "scripty": "^2.1.1", "sharp": "^0.32.6", "ts-node": "^10.9.1", "typescript": "~5.3.3", "yargs": "^17.6.2"}}