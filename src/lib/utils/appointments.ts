import moment from 'moment';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { AppointmentClientModel } from '@bookr-technologies/api/models/AppointmentClientModel';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { BadgeProps } from '~/components/ui/Badge';
import { ThemeColorKeys } from '~/components/ui/ThemeProvider/Theme';

export const isStaffMember = (accountType?: AccountType) =>
    accountType === 'EMPLOYEE' || accountType === 'BUSINESS_OWNER';

export const isEmployee = (accountType?: AccountType) => accountType === 'EMPLOYEE';

export const isBusinessOwner = (accountType?: AccountType) => accountType === 'BUSINESS_OWNER';

export const appointmentColors: Record<string, ThemeColorKeys> = {
    cancelled: 'error',
    completed: 'success',
    confirmed: 'accent',
    noShow: 'warning',
};

export const getAppointments = (
    userAppointments: AppointmentModel[],
): Record<'PAST' | 'FUTURE', AppointmentModel[]> => {
    const appointments: Record<'PAST' | 'FUTURE', AppointmentModel[]> = { FUTURE: [], PAST: [] };
    const NOW = moment();
    userAppointments.forEach((appointment) => {
        const dateTime = moment(appointment.dateTime);
        if (dateTime.isBefore(NOW) || appointment.cancelled) {
            appointments['PAST'].push(appointment);
        } else {
            appointments['FUTURE'].push(appointment);
        }
    });
    appointments['PAST'] = appointments['PAST'].sort((a, b) => (a.dateTime < b.dateTime ? 1 : -1));
    appointments['FUTURE'] = appointments['FUTURE'].sort((a, b) => (a.dateTime > b.dateTime ? 1 : -1));
    return appointments;
};

export enum AppointmentStatus {
    Cancelled = 'cancelled',
    Completed = 'finished',
    Confirmed = 'confirmed',
    NoShow = 'noShow',
}

export const getAppointmentStatus = (
    appointment?: AppointmentModel,
    appointmentClient?: AppointmentClientModel,
): AppointmentStatus => {
    if (appointmentClient?.canceled) {
        return AppointmentStatus.Cancelled;
    }
    if (appointmentClient?.noShow) {
        return AppointmentStatus.NoShow;
    }

    if (appointmentClient && appointment) {
        const isAppointmentCompleted =
            moment(appointment.dateTime).add(appointment.service.duration, 'minutes').diff(moment()) < 0;

        if (isAppointmentCompleted) {
            return AppointmentStatus.Completed;
        }
        return AppointmentStatus.Confirmed;
    }

    if (appointment) {
        if (appointment.cancelled) {
            return AppointmentStatus.Cancelled;
        }

        if (appointment.noShow) {
            return AppointmentStatus.NoShow;
        }

        const isAppointmentCompleted =
            moment(appointment.dateTime).add(appointment.service.duration, 'minutes').diff(moment()) < 0;

        if (isAppointmentCompleted) {
            return AppointmentStatus.Completed;
        }
    }

    return AppointmentStatus.Confirmed;
};

export const getAppointmentsBadgeProps = (
    t: (key: string) => string,
    appointment?: AppointmentModel,
    appointmentClient?: AppointmentClientModel,
): BadgeProps => {
    const title = getAppointmentStatus(appointment, appointmentClient);

    return {
        title: t(title),
        color: appointmentColors[title],
    };
};
