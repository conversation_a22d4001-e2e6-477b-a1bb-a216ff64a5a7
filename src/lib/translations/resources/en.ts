/* eslint-disable camelcase */
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { ActivityType } from '@bookr-technologies/api/models/ActivityType';
import { multiline } from '../utils';

export const translation = {
    aboutBusiness: 'About business',
    aboutNews: 'About news',
    aboutUs: 'About us',
    absentClient: 'Absent Client',
    acceptsNewClients: 'Accepts new clients',
    acceptsOnlinePayment: 'Accepts online payment',
    acceptsOnlinePaymentInformation:
        'Before you can accept online payments, you need to set up your online payment account. Click here to set up your online payment account.',
    accountTypes: {
        [AccountType.BusinessOwner]: 'Business owner',
        [AccountType.Client]: 'Client',
        [AccountType.Employee]: 'Staff Member',
    },
    accountVerificationDialogMessage:
        "You'll need to open the link in the email to verify your account, after that you can comeback and continue the process.",
    accountVerificationDialogTitle: 'A confirmation email has been sent to your email address.',
    accountVerificationScreenMessage:
        'We need to verify if you are the owner of the account. You will receive an email with a link to verify your account.',
    active: 'active',
    activities: {
        [ActivityType.AppointmentCancelled]: {
            title: 'Appointment cancelled',
            message: '{{displayName}} cancelled the appointment: {{date}}, {{time}} for {{service}}.',
        },
        [ActivityType.AppointmentRescheduled]: {
            title: 'Appointment rescheduled',
            message: '{{displayName}} rescheduled the appointment for {{date}}, {{time}}: {{service}}.',
        },
        [ActivityType.NewAppointment]: {
            title: 'New appointment',
            message: '{{displayName}} booked an appointment on {{date}} at {{time}}: {{service}}',
        },
    },
    activityHistory: 'Activity history',
    activityMonth: 'Activity month',
    add: 'Add',
    addBookingToSubscription: 'Add bookings to your subscription',
    addBreak: 'Add break',
    addBreakName: 'Add break name',
    addClient: 'Add client',
    addClients: 'Add clients',
    addClientsSubtitle: 'Add your first client or create your first reservation',
    addClientsTitle: 'Add your first client',
    addColorToYourService: 'Add a color to your service',
    addComment: 'Add a comment',
    addedToCalendar: 'Appointment added to your calendar',
    addMeToWaitingList: 'Add me to the waiting list',
    addNew: 'Add new',
    addNewBooking: 'Add new booking',
    addNewBreak: 'Add new break',
    addNewClient: 'Add new client',
    addNewClientByName: 'Add "{{name}}"',
    addNoOfContacts: 'Add {{noOfContacts}} contacts',
    addNotes: 'Add notes',
    address1: 'Address 1',
    address2: 'Address 2',
    addToCalendar: 'Add to calendar',
    addYourVatInformation: 'Add your VAT information',
    addYourVatInformationMessage:
        'Please add your VAT (or CIF, CUI, identification number) information to be able to accept online payments.',
    agreePrivacyAndTermsMessage: multiline`
        <Text>By continuing, you agree with our</Text>
        <HStack>
            <PrivacyLink>Privacy Policy</PrivacyLink>
            <And>and</And>
            <TermsLink>Terms and Conditions</TermsLink>
            <Icon>🔒</Icon>
        </HStack>
    `,
    allCategories: 'All categories',
    allContacts: 'All contacts',
    allowNotifications: 'Allow notifications',
    allYourDataWillBeLost: 'All your data will be lost.',
    allYourFutureBookingsWillBeCancelled: 'All your future bookings will be cancelled.',
    allYourStaffMembersWillBeRemoved: 'All your staff members will be removed from your account.',
    andNMore: '+ {{count}} more',
    appointment: 'Appointment',
    appointmentCancelled: 'Appointment cancelled',
    appointmentCancelledMessage: '{{displayName}} cancelled the appointment: {{date}}, {{time}} for {{service}}.',
    appointmentDetails: 'Appointment details',
    appointmentNotRepeating: 'Not repeating',
    appointmentRepeatingBiWeekly: 'Bi-weekly',
    appointmentRepeatingDaily: 'Daily',
    appointmentRepeatingMonthly: 'Monthly',
    appointmentRepeatingThreeWeekly: 'Once every three weeks',
    appointmentRepeatingWeekly: 'Weekly',
    appointmentRescheduled: 'Appointment rescheduled',
    appointmentRescheduledMessage: '{{displayName}} rescheduled the appointment for {{date}}, {{time}}: {{service}}.',
    appointments: 'Appointments',
    appointmentsStoryHeadline: 'Book an appointment at your favourite business in minutes',
    appointmentSuccessfullyCreated: 'Appointment successfully created',
    appointmentSuccessfullyModified: 'Appointment successfully modified',
    appointmentUpdated: 'Your appointment has been updated to {{dateTime}}',
    askForVirtualTour: 'Request a virtual tour',
    'auth/too-many-requests': 'Too many attempts. Please try again later.',
    'auth/user-token-expired': 'Session expired. Please login again.',
    'auth/wrong-password': 'Invalid credentials. Please try again.',
    automaticNotifications: 'Automatic notifications',
    availability: 'Availability',
    availabilityCardCaption:
        'The number of days in the future for accepting bookings. For example, if you enter the value 14, then your clients will be able to make an appointment (starting from today) with max. 14 days in the future.',
    availabilityNrOfDays: 'Availability (no. of days)',
    availabilityScreenCaption: 'Set the availability for your working hours',
    bad: 'Unpleasant',
    barber: 'Barber',
    bestBusinesses: 'Best businesses',
    bestCategories: 'Best categories',
    billingHistory: 'Billing history',
    billingInfo: 'Billing info',
    block: 'Block',
    blockClient: 'Block client',
    blockInfo:
        '{{clientName}} will no longer be able to make appointments with you. You can unblock them at any time by tapping the "Unblock" button.',
    bookAgain: 'Book again',
    booking: 'Booking',
    bookNow: 'Book now',
    bookPhoneCall: 'Call for appointment',
    bookrFeedback: 'What do you think about Bookr?',
    break: 'Break',
    breakBegins: 'Break begins',
    breakBetweenServices: 'Break between services',
    breakCouldNotBeDeleted: "Break couldn't be deleted!",
    breakEnds: 'Break ends',
    breakName: 'Break name',
    breakWasSuccessfullyAdded: 'Break was successfully added',
    bugReport: 'BUG Report',
    business: 'Business',
    businessCategories: 'Business categories',
    businessDeleted: 'Business deleted',
    businessEmail: 'Business email',
    businessHidden: 'This business cannot be accessed at the moment',
    businessInfo: 'Business info',
    businessListing: 'Business Listing',
    businessLocation: 'Business location',
    businessManagement: 'Business Management',
    businessName: 'Business name',
    businessProfile: 'Business profile',
    businessStartingDate: 'Business starting date',
    businessStartingDateDescription:
        'You can set the starting date of your business. By adding a starting date, you can make your business available to clients from a specific date. You can leave this field empty if you want to make your business available right now.',
    buy: 'Buy',
    buyFor: 'Buy for {{value}}',
    buySmsMessages: 'Buy SMS messages',
    calendar: 'Calendar',
    calendarStoryHeadline: 'Easy and fast appointments for your business and your clients',
    call: 'Call',
    cancel: 'Cancel',
    cancelAllAppointmentsSubtitle: 'Going back will cancel all appointments. Are you sure you want to continue?',
    cancelAllAppointmentsTitle: 'Are you sure you want to cancel the subscription?',
    cancelAppointment: 'Cancel Appointment',
    cancelAppointmentTitle: 'Do you want to cancel the appointment?',
    cancelled: 'Cancelled',
    cancelSubscription: 'Cancel subscription',
    cancelSubscriptionConfirmationCaption:
        'After canceling your subscription, you will need to choose a new plan to continue using Bookr.',
    cancelSubscriptionConfirmationTitle: 'Are you sure you want to cancel your subscription?',
    cannotCancelAppointmentAnymore: 'You cannot cancel this appointment anymore.',
    cannotLoadClient: 'Cannot load client',
    cannotLoadClients: 'Cannot load clients',
    categories: {
        barber: 'Barber',
        cosmetics: 'Cosmetics',
        coworking: 'Coworking',
        dentistry: 'Dentistry',
        dermatology: 'Dermatology',
        event: 'Event',
        hairstyling: 'Hairstyling',
        makeup: 'Make-up',
        manicure: 'Manicure',
        massage: 'Massage',
        nutrition_dietetics: 'Nutrition & Dietetics',
        ophthalmology: 'Ophthalmology',
        other: 'Other',
        photography: 'Photography',
        psychologist: 'Psychologist',
        body_remodeling: 'Body remodeling',
        sport: 'Sport',
        surgery: 'Surgery',
        tattoos: 'Tattoos',
        videography: 'Videography',
        orl: 'ORL',
        implant: 'Hair implants',
        podiatry: 'Podiatry',
        gynecology: 'Gynecology',
        plastic_surgery: 'Plastic surgery',
        permanent_hair_removal: 'Permanent hair removal',
        mentoring: 'Mentoring',
        coaching: 'Coaching',
        consultancy: 'Consultancy',
    },
    categoriesLabel: 'Categories',
    categoriesStoryHeadline: 'All businesses in one place',
    categoriesWereUpdatedSuccessfully: 'Categories were updated successfully',
    changeLanguage: 'Change language',
    changePass: 'Change password',
    changesWereSuccessfullySaved: 'Changes were successfully saved!',
    charactersLeft: '{{value}} characters left',
    childAbuse: 'Child abuse',
    chooseAccountTypeDescription: 'Choose one of the following account types to continue with your registration.',
    chooseAccountTypeHeadline: 'What do you use Bookr for?',
    chooseAPlan: 'Choose a plan',
    chooseBusinessDescription:
        'Suitable for people who own a business and want to book appointments with their clients.',
    chooseDay: 'Choose day',
    chooseHour: 'Choose time',
    chooseInterval: 'Choose interval',
    choosePaymentMethod: 'Choose payment method',
    choosePaymentMethodDescription:
        'In order to confirm your appointment, you need to choose a payment method. You can choose to pay with your credit card or with cash at the business location.',
    choosePersonalDescription: 'Suitable for people who want to book appointments for themselves at our businesses.',
    choosePlan: 'Choose $t(subscriptionPlans.{{plan}}.name)',
    chooseRecurringEndDay: 'Choose recurring end day',
    chooseStaffMember: 'Choose a staff member',
    chooseSubscriptionPlan: 'Choose a subscription plan',
    chooseSubscriptionPlanDescription: 'Choose one of our subscription plans to boost your business to the next level.',
    chooseSubscriptionPlanHeadline: 'Choose a subscription plan',
    chooseSubscriptionPlanTerms: multiline`
<Text>For more details about your contract, please read about our</Text>
<TermsLink>Terms and Conditions</TermsLink>
<Text>and about</Text>
<CancelLink>cancelling the subscription.</CancelLink>
`,
    chooseTeamMember: 'Choose team member',
    chooseYourDate: 'Choose a date for your appointment',
    chooseYourTime: 'Choose the time',
    chosenService: 'Selected service',
    city: 'City',
    clientDetails: 'Client details',
    clientDocuments: 'Clients documents',
    clientEmail: 'Client email',
    clientHistory: 'Customer record',
    clientImport: 'Import clients',
    clientImportFailed: 'Client could not be added',
    clientImportStarted: 'You will receive an email when the import is complete.',
    clientIsNotSelected: 'Client is not selected',
    clientListing: 'Clients list',
    clientName: 'Client name',
    clients: 'Clients',
    closed: 'Closed',
    commission: 'Commission',
    communityAccess: 'Access to the community',
    completed: 'Completed',
    compliment: 'Compliment',
    configured: 'Configured',
    configureOnlinePayment: 'Setup online payment',
    confirmationSent: 'Confirmation sent',
    confirmed: 'Confirmed',
    confirmYourPassword: 'Confirm your password',
    congratulations: 'Congratulations 🎉',
    contactDetails: 'Contact details',
    contactPayments: 'I have a question about payments',
    contactPaymentsDescription: 'Please contact the payments <NAME_EMAIL> for more information.',
    contactPermission: 'In order to import clients, we need access to your contacts',
    content: 'Content',
    continue: 'Continue',
    continueWithApple: 'Continue with Apple',
    continueWithEmail: 'Continue with Email',
    continueWithFacebook: 'Continue with Facebook',
    continueWithGoogle: 'Continue with Google',
    copyLink: 'Copy link',
    couldNotSubscribeToWaitingList:
        'Could not subscribe to waiting list. Please make sure you enabled push notifications.',
    country: 'Country',
    createAppointmentScreen: {
        headline: 'Create an appointment',
    },
    createAService: 'Create service',
    createAVideoCallForThisService: 'Create a video call',
    createBreakScreen: {
        headline: 'Add a break',
    },
    createdAt: 'Created at',
    createNewService: 'Create a new service',
    createPass: 'Create a password for your account. It must be at least 8 characters long.',
    createPassword: 'Create a password',
    createService: 'Create the services you offer for your clients. You can skip this step for the moment.',
    createServices: 'Add services',
    crm: 'Clients Management (CRM)',
    currencies: {
        LEI: 'Lei (RON)',
        EUR: 'Euro (€)',
        USD: 'USD ($)',
        GBP: 'Pound (£)',
    },
    currency: 'Currency',
    currentPlan: 'Your current plan',
    customNotifications: 'Custom notifications',
    dateAndTime: 'Date & Time',
    dateOfBirth: 'Date of birth',
    days: {
        friday: 'Friday',
        monday: 'Monday',
        saturday: 'Saturday',
        sunday: 'Sunday',
        thursday: 'Thursday',
        tuesday: 'Tuesday',
        wednesday: 'Wednesday',
    },
    daysInAdvanceCanBeBooked: 'Service availability period',
    daysInAdvanceSessionsCanBeBookedDescription:
        'This service is of type subscription. Please choose the number of days in advance that a session can be booked.\n\nFor example, if the number of sessions is 4 and the period is set to 30 days, then the client can book 4 sessions in the next 30 days.',
    daysLabel: 'Days',
    delete: 'Delete',
    deleteAccount: 'Delete Account',
    deleteAccountSideEffects: 'Deleting your account will do the following:',
    deleteBreak: 'Delete break',
    deleteClient: 'Delete client',
    deleteDocument: 'Delete document',
    deleteEveryRecurrence: 'Delete every recurrence after this date',
    deleteInfo:
        '{{clientName}} will be permanently deleted from your client list. You can add them again at any time by tapping the "Add" button.',
    deleteRecurrentAppointmentTitle: 'Delete recurrent appointment',
    deleteServiceHeadline: 'Do you really want to delete this service?',
    deleteThisAppointment: 'Delete this appointment',
    description: 'Description',
    displayName: 'Display name',
    documentAdded: 'Document added successfully',
    documentDeleted: 'Document deleted successfully',
    documents: 'Documents',
    donate: 'Donate',
    done: 'Done',
    downloadDocument: 'Download document',
    downloadingDocument: 'Downloading document...',
    doYouWantToDeleteThisImage: 'Do you want to delete this image?',
    doYouWantToDeleteThisStaffMember: 'Do you want to delete this staff member?',
    duration: 'Duration (minutes)',
    edit: 'Edit',
    editAppointment: 'Edit appointment',
    editNote: 'Edit note',
    editProfile: 'Edit profile',
    editTime: 'Update date & time',
    email: 'Email',
    emailAddress: 'Email Address',
    employee: 'Employee',
    'en-EN': 'English (UK)',
    'en-US': 'English (US)',
    enableSmsReminders: 'Enable SMS notifications',
    enableSmsRemindersCaption:
        'If you enable SMS notifications, your clients will receive appointment confirmation via SMS.',
    enterYourEmail: 'Enter your email',
    errorCreatingAccount: 'Error creating account',
    errorSavingToCalendar: 'Error saving to calendar',
    errorSendingInvitation: 'Something went wrong, invitation was not sent',
    explore3DTour: 'Explore 3D tour',
    exploreNewBusinesses: 'Explore new businesses',
    facebookUrl: 'Facebook URL',
    faq: 'FAQ',
    faqAnswer1:
        'Services are paid through a configured bank account. The business owner can set up a single bank account for the whole business, or allow each staff member to add their individual bank account.',
    faqAnswer2:
        'Online payment can only be added for simple services. Subscription-based services cannot be paid online.',
    faqAnswer3: 'The payment processor transfers payments to the configured account in 1-3 days.',
    faqAnswer4: 'The customer pays for the appointment at the time of creating it.',
    faqAnswer5:
        'The business (or you) pays a fee to the payment processor for each transaction. The fee is 2.9% + 0.25€ for each transaction. Bookr adds its own fee of 0.60€ for each transaction.',
    faqAnswer6:
        "If a staff member cancels an appointment, the payment will be refunded to the customer's account within 7-10 business days.",
    faqAnswer7:
        "If a customer cancels an appointment, the payment will be refunded to the customer's account within 7-10 business days only if the cancellation is made at least a certain number of hours before the appointment. This value can be set in your profile.",
    faqAnswer8: 'Online payment can be stopped by disabling it for each service you offer.',
    faqQuestion1: 'How are the services paid?',
    faqQuestion2: 'For what type of services can I add online payment?',
    faqQuestion3: 'How long does it take to receive payment for a service?',
    faqQuestion4: 'When will the client pay for the service?',
    faqQuestion5: 'What are the fees?',
    faqQuestion6: 'What happens if a staff member cancels an appointment?',
    faqQuestion7: 'What happens if a customer cancels an appointment?',
    faqQuestion8: 'How can I stop receiving online payments?',
    favouriteAdded: 'Added to favourites',
    favouriteError: 'Error adding to favourites',
    favouriteRemoved: 'Removed from favourites',
    favourites: 'Favourites',
    feedback: 'Feedback',
    feedbackCategory: 'Please select your feedback category below',
    feedbackUs: 'Tell us what you think about us...',
    female: 'Female',
    fetchingClients: 'Loading clients',
    fetchingClientsNoData: 'No clients found',
    filterByRating: 'Filter by rating',
    finishAccount: 'Finish account',
    finished: 'Finished',
    finishSubscriptionBooking: 'Finish subscription',
    firstNameAndLastName: 'First name and last name',
    followUsOnInstagram: 'Follow us on Instagram',
    forbiddenAccess: 'Forbidden access',
    forBusiness: 'For business',
    forBusinessesStoryHeadline: 'Manage your clients appointments in one place.',
    forgotPassword: 'Forgot password?',
    FREE_ACCESS: 'Silver',
    free_things: 'Only basic features',
    from: 'From',
    fromContacts: 'From contacts',
    gallery: 'Gallery',
    gender: 'Gender',
    general: 'General',
    generalInformation: 'General Information',
    getDirections: 'Get directions',
    getThereWithUber: 'Get there with Uber',
    good: 'Amazing',
    googleIntegration: 'Google Integration',
    googleReviews: 'Google Reviews',
    gotIt: 'Got it',
    grantAccess: 'Grant access',
    groupService: 'Group Service',
    groupServiceLabel: 'This service is designed for groups',
    harmfulDangerousActs: 'Harmful dangerous acts',
    hatefulOrAbusiveContent: 'Hateful or abusive content',
    haveYouTypedAWrongEmail: 'Wrong email?',
    hello: 'Hello 👋',
    helloWithName: 'Hello, {{displayName}}!',
    help: 'Help',
    helpUsImproveTheApp: 'Help us improve Bookr',
    helpUsImproveTheAppDescription:
        'We would love to improve Bookr and make it better for you. Please take a few minutes to answer a few questions.',
    hideBusiness: 'Hide business',
    hideBusinessDescription:
        'When you enable this, your business will no longer appear in search. Deactivate to get more clients from our marketplace.',
    hideService: 'Hide service',
    history: 'history',
    home: 'Home',
    howCanWeImprove: 'How can we improve?',
    howToImprove: 'How can we improve?',
    id: 'ID',
    idValue: '#{{id}}',
    iHaveChangedMyMind: 'I have changed my mind',
    iHaveConfirmed: 'I have confirmed',
    imageAdded: 'Image added successfully',
    imageDeleted: 'Image deleted successfully',
    imReady: "I'm Ready",
    imReadySent: 'A notification has been sent to the client.',
    infringesMyRights: 'Infringes my rights',
    instagramUrl: 'Instagram URL',
    instantBooking: 'Instant booking',
    interests: {
        HAIRSTYLING: '💇🏻‍♀️ Hairstyling',
        BARBER: '💈 Barber',
        MAKEUP: '💄 Makeup',
        COSMETICS: '🧴 Cosmetics',
        MANICURE: '💅🏻 Manicure',
        MASSAGE: '💆🏻‍♀️ Massage',
        DENTISTRY: '🦷 Dentistry',
        OPHTHALMOLOGY: '👁 Ophtha',
        DERMATOLOGY: '🧖🏻‍♀️ Dermatology',
        SURGERY: '🩺 Surgery',
        PSYCHOLOGIST: '🧠 Psychologist',
        NUTRITION_DIETETICS: '🥑 Nutrition',
        BODY_REMODELING: '🏋🏻‍♀️ Body remodeling',
        IMPLANT: '🦷 Implant',
        PODIATRY: '👣 Podiatry',
        GYNECOLOGY: '👩🏻‍⚕️ Gynecology',
        PLASTIC_SURGERY: '🩺 Plastic surgery',
        PERMANENT_HAIR_REMOVAL: '🔥 Permanent hair removal',
        ORL: '👂🏻 ORL',
        MENTORING: '👨🏼‍🏫 Mentoring',
        COACHING: '👨🏼‍🏫 Coaching',
        CONSULTANCY: '👨🏼‍🏫 Consultancy',
        OTHER: '🔎 Other',
    },
    invalidEmail: 'Invalid email',
    inviteStaffMember: 'Invite staff member',
    inviteStaffMemberDescription: 'In order to invite a staff member, please enter their email address.',
    isRecurrence: 'Recurring',
    isVatRegistered: 'Are you VAT registered?',
    johnDoe: 'John Doe',
    joinBusiness: 'Join business',
    joinBusinessDescription: 'Tap the button below to join.',
    joinBusinessHeadline: '{{businessName}} invited you to join their business',
    joinTheMeeting: 'Join the meeting',
    keepMyAccountActive: 'Keep my account active',
    language: 'Language',
    latestSearches: 'Latest searches',
    limited: 'Limited',
    linkCopied: 'Link copied',
    location: 'Location',
    locationInformationCouldNotBeFetched: "We couldn't find any information about selected address.",
    locationPermissionsDenied: 'Permission to access location was denied',
    locationPermissionsDeniedMessage: 'The device does not have permission to access location.',
    locationWasUpdated: 'Location was updated',
    logout: 'Logout',
    mail: 'Mail',
    maintenanceCopy: 'With love, the Bookr team',
    maintenanceDescription: "We're currently working on improving things a little bit, we'll be back shortly.",
    maintenanceTitle: 'Thanks for being with us!',
    makeAppointment: 'Make an appointment',
    male: 'Male',
    marketing: 'Marketing',
    meetingLink: 'Meeting link',
    membersBreaks: 'Members breaks',
    message: 'Message',
    minLength: 'Value must have at least {{minLength}} characters',
    minNumberOfSessionsError: 'Minimum number of sessions must be greater than {{ value }}',
    minTimeBeforeAppointment: 'Minimum time prior to the appointment',
    minTimeBeforeAppointmentCardCaption:
        'Minimum time prior to the desired slot needed to book an appointment. For example, if the value is 60, then the client must book an appointment at least 60 minutes prior to the desired time. If the value is 0, then the client does not have a minimum time prior to the appointment.',
    minTimeBeforeCancel: 'Minimum time prior to cancel appointment',
    minTimeBeforeCancelCardCaption:
        'Minimum time prior to cancel appointment (in minutes). If the value is 60, then the client must cancel an appointment at least 60 minutes prior to the appointment time. If the value is 0, then the client does not have a minimum time prior to cancel appointment.',
    minutes: 'Minutes',
    moreOptions: 'More Options',
    multipleSelectPayment: 'Payout in multiple accounts',
    multipleSelectPaymentDescription:
        'If you enable this option, each staff member will receive the payout in their own account.',
    myAppointments: 'My appointments',
    myProfile: 'My Profile',
    nearbyBusinesses: 'Nearby',
    totalBusinessesNearby: '{{total}} businesses nearby',
    noBusinessesNearby: 'No businesses found nearby',
    needANewAccount: '<Typography> You need to</Typography><Link>create a new account!</Link>',
    needsSetup: 'Needs setup',
    newAppointment: 'New appointment',
    newAppointmentMessage: '{{displayName}} booked an appointment on {{date}} at {{time}}: {{service}}',
    newPass: 'New password',
    next: 'Next',
    nextAppointment: 'Next appointment',
    nextEvent: 'Next event',
    nextPaymentDate: 'Next payment: {{value}}',
    no: 'No',
    noAppointmentsCreateNewOne: 'Currently you have no upcoming appointments. Book an appointment now.',
    noAppointmentsYet: 'You have no appointments yet',
    noBillingHistory: 'No billing history',
    noBreaksYet: 'You have no breaks yet',
    noClients: 'No clients matched your search. Please try again with a different search.',
    noContactInformation: 'No contact information',
    noContacts: 'No contacts found',
    noDocuments: 'This client has no uploaded documents',
    noFavouritesYet: 'You have no favourite businesses yet',
    noNoteSaved: 'No notes saved',
    noNotifications: 'Currently you have no notifications',
    noPreviewAvailable: 'No preview available',
    noProfilePictureSelected: 'Select a profile picture',
    noServices: 'This staff has no services',
    noServicesFound: 'No services found',
    noShow: 'No show',
    noSmsOptionsFound: 'We could not load SMS options, try again later.',
    noStaffMembersFound: 'No staff members found',
    noSubscriptionFound: 'We could not load current subscription, try again later.',
    noteCreated: 'Note created',
    notes: 'Notes',
    noteUpdated: 'Note updated',
    notificationPermissionsDenied:
        'Notifications were not granted for this device, real-time notifications will not be available.',
    notificationPreferenceDescription:
        'You will receive real-time notifications for your appointments, and also promotional notifications for our latest offers.',
    notificationPreferenceHeadline: 'Would you like to receive notifications?',
    notifications: 'Notifications',
    notificationsError: 'Error retrieving your notifications',
    notificationsSettings: 'Notifications settings',
    noPersonRegistered: 'No one registered',
    noTimeSlotsAvailable: 'There are no time slots available for this day',
    notNow: 'Not now',
    numberOfDays: '{{value}} days',
    numberOfLeftSms: '{{value}} messages left',
    numberOfMessages: '{{value}} SMSes',
    numberOfMinutes: '{{value}} minutes',
    numberOfSessions: 'Number of sessions',
    oauthCancelled: 'Sign in was cancelled',
    oauthNoPermission: 'You do not have permission to sign in',
    ok: 'Ok',
    oldPass: 'Old password',
    onePersonRegistered: 'One person registered',
    oneStaffMember: 'Only one staff member',
    onlinePayments: 'Online payments',
    onlinePaymentsDescription: 'If you want to enable online payments, please use the button below.',
    onlinePaymentsHeader: 'This option allows your clients to pay online for their bookings.',
    onlineService: 'Online service',
    onlyAdminCanPay: 'Pay attention! Only the owner can pay the bill.',
    open: 'Open',
    or: 'or',
    other: 'Other',
    others: 'Others',
    ourTeamWillContactYouAsSoonAsPossible: 'Our team will contact you as soon as possible.',
    overlapAppointmentMessage:
        'An existing appointment (for {{displayName}}: {{start}} - {{end}}) overlaps with your selected time. Are you sure you want to add this appointment too?',
    overlapAppointmentsMessage:
        'There are {{count}} overlapping appointments:\n{{appointments}}\n Are you sure you want to add this appointment too?',
    overlapAppointmentTitle: 'Overlapping appointment(s)',
    ownerMustEnablePaymentsForAllStaff:
        'If you wish to get paid for your bookings in your own account, please contact the owner of the business to enable this setting.',
    passChangeSuccess: 'Password changed successfully',
    password: 'Password',
    passwordNotMatch: 'Passwords do not match',
    payAtLocation: 'Pay at location',
    payments: 'Payments',
    payNow: 'Pay now',
    payOnline: 'Pay online',
    peopleRegistered: 'People registered',
    performance: 'Performance',
    performanceIndicators: 'Performance indicators',
    performanceStats: {
        totalAppointments: 'Total appointments',
        finalizedAppointments: 'Finished appointments',
        totalClients: 'Total clients',
        cancelledAppointments: 'Cancelled appointments',
        totalTime: 'Total time',
        hours: '{{hours, number(maximumFractionDigits: 2)}} hours',
        workedHours: 'Worked hours',
        lostHours: 'Lost hours',
        reservedTime: 'Time booked',
        ron: '{{earnings}} lei',
        totalEarnings: 'Total earnings',
    },
    performanceStoryHeadline: 'Check out your performance directly from the app',
    personal: 'Personal',
    personalDetails: 'Personal details',
    phoneCallAppointment: 'Phone reservation',
    phoneCallAppointmentDescription:
        'This business does not have Instant Booking enabled. Please call them to book an appointment.',
    phoneNumber: 'Phone number',
    postalCode: 'Postal code',
    previous: 'Previous',
    price: 'Price',
    priceMustBePositive: 'Price must be positive',
    participantsMustBePositive: 'Number of participants must be 0 or greater',
    primaryInformation: 'Primary information',
    privacyPolicy: 'Privacy Policy',
    privilegeBasedAccessMultiple:
        '<Text>To access this page, you need one of the following plans: <strong>{{value}}</strong></Text>',
    privilegeBasedAccessSingle:
        '<Text>To access this page, you need to have the following plan: <strong>{{value}}</strong></Text>',
    privilegeBasedAccessTitle: 'Benefit of the entire Bookr platform',
    PROFESSIONAL_ACCESS: 'Platinum',
    professional_things: 'Suited for professionals',
    profile: 'Profile',
    profilePicturePickerScreenDescription:
        'Add a profile picture to your profile so that businesses will recognize you better.',
    profilePicturePickerScreenHeadline: 'Choose a profile picture',
    profileUpdateSuccess: 'Your profile has been updated successfully!',
    program: 'Schedule',
    promoLink: 'Promotion link',
    promoLinkDescription:
        'This link can be used to promote your business online and on social media. Your clients can use it to create appointments using directly this link.',
    promoLinkUse: 'How do I use this link?',
    promotesTerrorism: 'Promotes terrorism',
    publicProfileOfBusiness: 'Public profile',
    ratingNumber: '{{ rating, number(maximumFractionDigits: 2) }} Rating',
    readMore: 'Read more',
    receiveTicketSoon: 'Your reservation is confirmed. We will send you an email with the ticket soon.',
    recentBusinesses: 'New businesses',
    recoverPassword: 'Recover password!',
    recurrence: 'Recurrence',
    reminders: 'Reminders',
    reportBusiness: 'Report',
    reportContent: 'Report Content',
    reportContentError: 'The content was not reported. Please try again later',
    reportContentSuccess: 'The content was reported successfully',
    requestCalendarPermissionsAsyncNotGranted: 'Calendar permissions not granted',
    requestHasBeenSent: 'Request has been sent',
    requestRemindersPermissionsAsyncNotGranted: 'Reminders permissions not granted',
    requiredField: 'Required field',
    reschedule: 'Reschedule',
    resend: 'Resend',
    resendIn: 'Resend({{value}})',
    reservationDone: 'Booking made',
    resetPasswordSuccessMessage: 'We have sent you an email with a link to reset your password.',
    reviews: 'Reviews',
    reviewSent: 'Review sent successfully',
    reviewsFromOurUsers: 'All our reviews are based on real users who have already booked an appointment with us.',
    reviewsNumber: '{{ reviews }} reviews',
    reviewsYouCanTrust: 'Reviews you can trust',
    reviewUsOnAppStore: 'Review us on App Store',
    reviewUsOnPlayStore: 'Rate us on Play Store',
    reviewYourExperience: 'Review your experience ✨',
    'ro-RO': 'Română',
    salesReports: 'Sales Reports',
    save: 'Save',
    saveChanges: 'Save changes',
    saveComment: 'Save comment',
    saveLocation: 'Save location',
    saveNotes: 'Save notes',
    schedule: 'Schedule',
    searchClients: 'Search for a client',
    searchServiceOrLocation: 'Search for a service or location',
    seeAllBreaks: 'See all breaks',
    seeAllReviews: 'See all reviews',

    selectEventTypeScreen: {
        appointment: '$t(appointment)',
        appointmentDescription: 'Make an appointment for your clients in a few seconds',
        break: '$t(break)',
        breakDescription: 'Everyone needs a short break. Create a break for you now.',
        description: 'Choose one of the below options',
        headline: 'What would you like to add?',
    },
    sendFeedback: 'Send feedback',
    sendInvitation: 'Send Invitation',

    sendMail: 'Send mail',
    sendPaymentLinkToCustomer: 'This appointment must be paid online. Please send the payment link to the customer.',

    sendReview: 'Send review',
    sendSMS: 'Send SMS',

    sendSmsMessages: 'Send SMS messages.',

    sendSmsMessagesCaption:
        'If you enable SMS notifications, your clients will receive a text message for their appointment.',
    service: 'Service',
    serviceAvailableForOnlinePayments: 'Service available for online payments',
    serviceCreatesVideoCall: 'This service creates a video call',

    serviceDeleted: 'Service successfully deleted!',
    serviceDescription: 'Service Description',
    serviceDurationAndPrice: '{{item.duration}} min • {{item.price}} {{item.currency}}',
    serviceHiddenForClients: 'Service hidden for clients',
    serviceIsNotSelected: 'Service is not selected',
    serviceName: 'Service name',
    services: 'Services',
    settings: 'Settings',
    settingsProfileCategoriesScreenCaption: 'Choose at least one category',
    settingsProfileCategoriesScreenHeadline: 'Your business categories',
    settingsProfileWorkingProgramBusinessDescription:
        'Choose the working hours of your business. You can update this at anytime.',
    settingsProfileWorkingProgramStaffMemberDescription: 'Choose your working hours. You can update this at anytime.',
    settingsUpdated: 'Your settings have been updated',
    sexualContent: 'Sexual Content',
    showAllAppointments: 'Show all appointments',
    showAllCategories: 'See all categories',
    showLess: 'Show less',
    showMore: 'Show more',
    signInScreenDescription: 'Sign in using your email address',
    signInScreenHeadline: 'Sign in',
    signout: 'Sign out',
    signUpBusinessCategoriesDescription: 'Choose at least one category',
    signUpBusinessCategoriesHeadline: 'What kind of business are you?',
    signUpBusinessDetailsDescription: 'We need some basic information about your business',
    signUpBusinessDetailsHeadline: 'About your business',
    signUpBusinessLocationHeadline: 'Where is your business located?',
    signUpBusinessProgramDescription: 'Choose your working hours',
    signUpBusinessProgramHeadline: 'Your business working hours',
    signUpScreenDescription: 'Enter your information to finish your registration',
    signUpScreenHeadline: "Let's get started",
    skip: 'Skip',
    smsBalance: 'SMS Balance',
    smsBalanceDescription: 'Make sure you have enough SMS credits to send your clients SMS messages.',
    smsBought: 'You bought {{count}} SMS',
    smsNotifications: 'SMS notifications',
    socialMedia: 'Social Media',
    somethingWentWrong: 'Something went wrong',
    somethingWentWrongError: 'Something went wrong, $t({{error}})',
    spamOrMisleading: 'Spam or misleading',
    staffMember: 'Staff member',
    staffMemberInvitationSent: 'An invitation has been sent to the staff member',
    staffMemberIsNotSelected: 'Staff member is not selected',
    staffMembers: 'Staff members',
    staffMembersOrderCouldNotBeSaved: 'Staff members order could not be saved',
    staffMembersTop: 'Top Professionals',
    staffName: 'Staff name',
    STANDARD_ACCESS: 'Gold',
    standard_things: 'Suited for small businesses',
    startNow: 'Start now',
    statistics: 'Statistics',
    status: 'Status',
    subscribedSuccessfullyToWaitingList: 'You have been subscribed successfully to the waiting list',
    subscription: 'Subscription',
    subscriptionPlans: {
        custom: {
            description: 'Custom solutions for your business',
            name: 'Custom',
            price: 'Custom price',
        },
        free: {
            description: 'Silver',
            name: 'Silver',
        },
        professional: {
            description: 'Platinum',
            name: 'Platinum',
        },
        standard: {
            description: 'Gold',
            name: 'Gold',
        },
    },
    suggestions: 'Suggestions',
    support: 'Support',
    sureBlock: 'Are you sure you want to block {{clientName}}?',
    sureDelete: 'Are you sure you want to delete {{clientName}}?',
    switchToBusiness: 'Switch to business',
    switchToClient: 'Switch to client',
    taxId: 'Tax ID (VAT code)',
    teamManagement: 'Team management',
    technicalSupport: 'Technical support',
    termsAndConditions: 'Terms & Conditions',
    textMessages: 'Text messages (SMS)',
    thanksBookr: 'Thank you for using Bookr!',
    thanksForFeedback: 'Thank you for your feedback!',
    thanksForHelpingUs: 'Thank you for helping us improve Bookr!',
    timezoneMessage: 'All times are displayed in the timezone of the business location.',
    to: 'To',
    totalBookings: 'Bookings',
    totalCancelled: 'Cancelled',
    totalFinished: 'Finished',
    totalNoShows: 'No shows',
    totalResults: '{{totalResults}} results',
    totalRevenueFromClient: 'Revenue from client:',
    totalVisits: 'Total Visits',
    trialPeriod: 'Trial period',
    typeBusinessLocation: 'Type your business location',
    typeToSearch: 'Type to search...',
    unblockClient: 'Unblock client',
    unknown: 'Unknown',
    unlimitedAppointments: 'Unlimited bookings',
    unlimitedStaffMembers: 'Unlimited staff members',
    unpaidBillMessage: 'If you want to continue using Bookr, please pay your bill for the previous month.',
    unpaidBillTitle: 'Continue to use Bookr!',
    updateOnlinePayment: 'Update online payment',
    upgradePlan: 'Upgrade plan',
    uploadDocument: 'Upload a document',
    uploadImage: 'Upload an image',
    userNotFoundError: "Something went wrong, you are not logged in or you don't have access to this resource!",
    verificationEmailSent: 'A verification email has been sent to your email address.',
    verificationLinkSendOn: 'Verification link sent to:',
    viewAppointment: 'View Appointment',
    viewDemo: 'View Demo',
    viewDetails: 'View details',
    viewLocation: 'View location',
    violentOrRepulsiveContent: 'Violent or repulsive content',
    virtual3dTourCaption: 'We are the only app in the world that offers a virtual 3D tour of your business.',
    virtual3dTourHeadline: '3D Virtual tour',
    virtualTour: '3D Virtual tour',
    waitingClients: 'Waiting clients',
    waitingList: 'Waiting list',
    waitingListClients: 'Waiting list',
    websiteUrl: 'Website URL',
    weCouldNotFindAnyBusinessForThisAppointment: 'We could not find any business for this appointment',
    weCouldNotFindAnyEmailForThisAppointment: 'We could not find any email for this appointment',
    weCouldNotFindAnyPhoneNumberForThisAppointment: 'We could not find any phone number for this appointment',
    weCouldNotFindAnyServices: 'We could not find any services',
    weCouldNotFindAnyServicesForTheSelectedStaffMember: 'We could not find any services for the selected staff member.',
    weCouldNotFindYourBusiness: 'We could not find your business',
    welcomeToBookr: 'Welcome to Bookr',
    welcomeToBookrStoryHeadline: 'Book from everywhere, anywhere.',
    weWillNotBeAbleToRecoverYourAccount: 'We will not be able to recover your account after deletion.',
    whyDoYouUseBookr: 'Choose what you would like to schedule',
    with: 'with',
    withPhoneNumber: 'With phone number',
    workingHours: 'Your working hours',
    workingProgramCouldNotBeSaved: 'Working program could not be saved!',
    workingSchedule: 'Working schedule',
    xOutOfY: '{{x}} out of {{y}}',
    yes: 'Yes',
    yesCancel: 'Yes, cancel',
    youCanAskTheStaffMemberToCreateAService: 'You can ask the staff member to create a service',
    yourAccountIsNotVerified: 'Your account is not verified, please check your email for a verification link.',
    yourContacts: 'Your contacts',
    yourDevicesWillNotBeAbleToAccessYourAccount: 'Your devices will not be able to access your account.',
    yourExperienceWith: 'Leave a review for {{displayName}}',
    yourFavourites: 'Your Favourites',
    yourInformation: 'Your information',
    yourPersonalWebsite: 'Own website',
    yourServices: 'Your services',
    getMoreClientsBanner: 'Get more clients with Bookr',
    completeProfileToGetMoreClients: 'Complete your profile by adding services and working hours to get more clients.',
    tapToLearnMore: 'Tap to learn more',
    goToBusinessProfile: 'Go to business profile',
    continueAsGuest: 'Continue as guest',
    search: 'Search',
    filters: 'Filters',
    sortBy: 'Sort by',
    recommended: 'Recommended',
    distance: 'Closest',
    rating: 'Best rated',
    minPrice: 'Lowest price',
    businessType: 'Business type',
    allBusinesses: 'All businesses',
    instantBookingOnly: 'Instant booking only',
    businessTypeTitle: 'What are businesses with instant bookings?',
    businessTypeDescription:
        'These businesses are verified and official partners of Bookr. Only for these businesses you have the possibility to schedule directly through our application. By choosing businesses from the "Instant Booking" category, you will benefit from a quick and easy experience to book the desired services.',
    whyToChooseInstantBooking: 'Why choose "Instant Booking"?',
    whyToChooseInstantBookingDescription1:
        '<Text><Bold>Schedule Directly</Bold>: Only "Instant Booking" businesses allow you to schedule directly through the Bookr app, saving time and providing you with immediate confirmation.</Text>',
    whyToChooseInstantBookingDescription2:
        '<Text><Bold>Verified Businesses</Bold>: Businesses in this category are verified partners of our team, guaranteeing superior quality of services.</Text>',
    minimum: 'Minimum',
    maximum: 'Maximum',
    maxParticipantsPerGroup: 'Maximum Participants per Group',
    priceRange: 'Price range',
    reset: 'Reset',
    whatAreYouLookingFor: 'What are you looking for?',
    noResultsForText: 'No results found for "{{text}}"',
    tryDifferentSearch: 'Please try a different search.',
    searchForAddress: 'Search for an address',
    streetLocationEtc: 'Street, location, etc.',
    currentLocation: 'Current location',
    anyService: 'Any service',
    errorSearchingTryAgain: 'Oups! Something went wrong. Please try again.',
    limits: 'Limits',
    appointmentLimits: 'Appointment limits',
    appointmentLimitsDescription:
        'The Silver plan allows you to manage up to 100 appointments. If your business grows and you need more appointments, you can upgrade to a plan that suits your needs.',
    upgradeLimit: 'Upgrade limit',
    outOf100Appointments: 'out of 100 appointments',
    appointmentsLimit100: '100 appointments limit',
    shortcuts: 'Shortcuts',
    sendANotification: 'Send a notification',
    nextStep: 'Next step',
    selectClients: 'Select clients',
    selectClientsToSendNotification: 'Only clients with push notifications enabled appear in the list.',
    selectAllClients: 'Select all clients',
    composeNotification: 'Compose your notification',
    composeYourNotification: 'Enter the message you want to send to your clients.',
    notificationMessage: 'Notification message',
    maxCharacters: 'max {{max}} characters',
    typeYourMessage: 'Type your message',
    confirmNotification: 'Confirm notification',
    confirmNotificationCaption: 'Verify the message before sending it to your clients.',
    sendNotification: 'Send Notification',
    previewNotification: 'Preview notification',
    numberOfClients: '{{count}} clients',
    yourBusinessLimit: 'Your business limit',
    yourBusinessLimitCaption:
        'This feature is in beta. Thus, businesses can currently send notifications to a limited number of clients per day.',
    yourNotificationsUsage:
        '<Text>Your business has sent <Bold>{{used}} out of {{total}}</Bold> notifications today.</Text>',
    errorPreviewingNotification: 'Error previewing notification',
    notificationSent: 'Notification sent',
    errorSendingNotification: 'Error sending notification',
    privilegeBasedAccessSubtitlePlans: 'This feature is available only for the following plans: {{plans}}',
    date: 'Date',
    anyDate: 'Any date',
    time: 'Time',
    anyTime: 'Any time',
    today: 'Today',
    tomorrow: 'Tomorrow',
    morning: 'Morning',
    afternoon: 'Afternoon',
    evening: 'Evening',
    selectTime: 'Select time',
    searchServiceDurationAndMember: '<Text>{{duration}} mins  ∙  <Bold>{{staffMember}}</Bold></Text>',
    viewMoreOnProfile: 'View more on business profile',
    now: 'Now',
    howDoesNowFeatureWork: 'How does the "Now" feature work?',
    howDoesNowFeatureWorkDescription1:
        'When you activate the "Now" feature, we will show you only the available appointments for today, in the next 3-4 hours. This helps you quickly find free slots at salons and specialists near you, to schedule without waiting.',
    whyToUseNowFeature: 'Why use "Now"?',
    whyToUseNowFeatureDescription1:
        '<Text><Bold>Flexibility</Bold>: Quickly find available options for a last-minute appointment.</Text>',
    whyToUseNowFeatureDescription2:
        '<Text><Bold>Time-saving</Bold>: Without wasting time searching through available hours in the following days.</Text>',
    whyToUseNowFeatureDescription3:
        '<Text><Bold>Convenience</Bold>: Perfect for unforeseen situations or when you have unexpected free time.</Text>',
    yourSearchResults: 'Your search results',
    appointmentLimitsDescriptionReset: 'The limit will reset on: {{date}}.',
    limitReachedTitle: 'You have reached the limit of 100 appointments!',
    limitReachedDescription:
        'If you want to add more appointments, we invite you to upgrade to a plan that suits your needs.',
    limitAlmostReachedTitle: 'You are almost at the limit of 100 appointments.',
    limitAlmostReachedDescription: 'Upgrade to continue scheduling your clients without interruptions!',
    paidOnline: 'Paid online',
    textMessagesDescription: 'Send text messages to your clients to remind them of their appointments.',
    configureSms: 'Configure SMS',
    configureSmsDescription: 'Configure your SMS settings to send notifications to your clients.',
    chooseStaffMembers: 'Choose staff members',
    chooseNotificationTypes: 'Choose notification types',
    chooseNotificationTypesDescription:
        'Choose the types of notifications you want to send to your clients. You can select multiple types.',
    chooseStaffMembersDescription:
        'Choose the staff members who will send SMS notifications to their customers. Only the customers of the chosen staff members will receive SMS notifications.',
    notificationTypes: {
        NEW_APPOINTMENT: 'New appointment',
        APPOINTMENT_RESCHEDULED: 'Appointment rescheduled',
        APPOINTMENT_CANCELLED: 'Appointment cancellation',
        APPOINTMENT_REMINDER: 'Appointment reminder',
    },
    enabled: 'Enabled',
    disabled: 'Disabled',
    acceptsNewClientsCardCaption: 'If you want to accept new clients in your staff member profile, enable this option.',
    currentlyNotAcceptingNewClients: 'Currently not accepting new clients',
    searchServiceByName: 'Search for services by name',
};
