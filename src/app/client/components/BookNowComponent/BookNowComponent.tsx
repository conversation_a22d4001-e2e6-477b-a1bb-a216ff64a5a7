/* eslint-disable @typescript-eslint/no-explicit-any */
import { AxiosError } from 'axios';
import moment from 'moment';
import { ReactElement, useCallback, useMemo, useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useTheme } from 'styled-components';
import { BookNowRequest, BookNowResponse } from '@bookr-technologies/api/dto/BookNowDTO';
import { TimeslotDTO } from '@bookr-technologies/api/dto/TimeslotDTO';
import { appointmentsEndpoint } from '@bookr-technologies/api/endpoints/appointmentsEndpoint';
import { waitingListEndpoint } from '@bookr-technologies/api/endpoints/waitingListEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { Calendar, DateData } from '~/components/Calendar';
import { SelectableButton } from '~/components/SelectableButton';
import { Button } from '~/components/ui/Button';
import { Grid, HStack, VStack } from '~/components/ui/Grid';
import { Screen, ScreenHeader, ScreenHeaderBackButton } from '~/components/ui/Screen';
import { Typography } from '~/components/ui/Typography';
import { convertTimesToBusinessTimezone } from '~/components/utils/time';
import { useContainerPadding } from '~/hooks/useContainerPadding';
import { useLayout } from '~/hooks/useLayout';
import { useLocalPushNotificationScheduler } from '~/hooks/useLocalPushNotificationScheduler';
import { useNotifications } from '~/hooks/useNotifications';
import { useSafeAreaInsets } from '~/hooks/useSafeAreaInsets';
import { useI18n } from '~/hooks/useTranslation';
import { useUser } from '~/hooks/useUser';
import { AnalyticsEvent, logAnalyticsEvent } from '~/lib/analytics/analytics';
import { reactQueryClient } from '~/lib/clients/reactQuery';
import { TranslationKeys } from '~/lib/translations/keys';
import { getMaxStaffAppointmentDate, getMinStaffAppointmentDate } from '~/lib/utils/business';
import { addToCalendar, deleteCalendarEvent, existsCalendarEvent } from '~/lib/utils/calendar';

interface BookNowComponentParams {
    appointmentToEdit?: AppointmentModel;
    business: BusinessModel;
    hideHeader?: boolean;
    isEditAppointment?: boolean;
    navigate: any;
    onBookingSuccess?: (appointment: AppointmentModel) => void;
    service: ServiceModel;
    staff: UserModel;
}

export function BookNowComponent({
    appointmentToEdit,
    business,
    hideHeader,
    isEditAppointment,
    navigate,
    onBookingSuccess,
    staff,
    service,
}: BookNowComponentParams): ReactElement {
    const user = useUser();
    const theme = useTheme();
    const t = useI18n();
    const layout = useLayout();
    const insets = useSafeAreaInsets(true);
    const containerPadding = useContainerPadding();
    const notifications = useNotifications();
    const notificationScheduler = useLocalPushNotificationScheduler();
    const staffId = staff.uid;
    const serviceId = String(service.id);
    const [timestamp, setTimestamp] = useState(-1);
    const [timeslot, setTimeslot] = useState<TimeslotDTO | null>(null);
    const [current, setCurrent] = useState(moment().format('YYYY-MM-DD'));
    const { data: timeslotsData, isLoading: isTimeslotsLoading } = useQuery({
        queryKey: ['timeslots', staffId, serviceId, current],
        queryFn: () => appointmentsEndpoint.generateTimeslots(current, staffId, serviceId),
        cacheTime: 60000,
    });
    const { data: datesThatCanBeBooked } = useQuery({
        queryKey: ['datesThatCanBeBooked', staffId, serviceId],
        queryFn: () => appointmentsEndpoint.daysThatCanBeBooked(staffId, serviceId),
    });

    const bookNowMutation = useMutation(
        'bookNow',
        (bookNowRequest: BookNowRequest) =>
            appointmentsEndpoint.create<BookNowResponse, BookNowRequest>(bookNowRequest),
        {
            retry: false,
            onSuccess: async ({ error, success, appointment }: BookNowResponse) => {
                if (error) {
                    notifications.error(error);
                    // this will invalidate and refetch the timeslots
                    reactQueryClient.invalidateQueries([`timeslots:${staffId}:${serviceId}:${current}`]);
                    // await refetchTimeslots();
                } else if (success) {
                    logAnalyticsEvent(AnalyticsEvent.BookingMade);
                    await notificationScheduler.scheduleAppointmentReviewPushNotification(appointment);
                    if (onBookingSuccess) {
                        onBookingSuccess(appointment);
                    } else {
                        if (appointment.service.acceptsOnlinePayments) {
                            return navigate('AppointmentChoosePaymentScreen', {
                                appointments: [appointment],
                                showAddToCalendar: true,
                                addToCalendar: addAppointmentToCalendar,
                                onGoToAppointment: (appointment: AppointmentModel) =>
                                    navigate('AppointmentDetailsScreen', { appointment, client: user }),
                            });
                        }

                        return navigate('ClientApplication', {
                            screen: 'Home',
                            params: {
                                actionAfterLoad: () => {
                                    return navigate('AppointmentConfirmationScreen', {
                                        appointments: [appointment],
                                        showAddToCalendar: true,
                                        addToCalendar: addAppointmentToCalendar,
                                        onGoToAppointment: (appointment: AppointmentModel) =>
                                            navigate('AppointmentDetailsScreen', { appointment, client: user }),
                                    });
                                },
                            },
                        });
                    }
                }
            },
            onError: (error: AxiosError) => {
                const message = getErrorMessage(error, 'somethingWentWrong');

                notifications.error(t(message));
            },
        },
    );

    const updateAppointmentMutation = useMutation(
        'updateAppointment',
        () => {
            if (!appointmentToEdit) {
                return Promise.reject(t('somethingWentWrong'));
            }

            return appointmentsEndpoint.update<AppointmentModel, { timestamp: number }>(appointmentToEdit.id, {
                timestamp,
            });
        },
        {
            retry: false,
            onSuccess: async (appointment: AppointmentModel) => {
                await Promise.all([
                    notificationScheduler.cancelAppointmentReviewPushNotification(appointment),
                    notificationScheduler.scheduleAppointmentReviewPushNotification(appointment),
                ]);

                try {
                    const isCreatedInCalendar = await existsCalendarEvent(String(appointment.id));
                    if (isCreatedInCalendar) {
                        await deleteCalendarEvent(String(appointment.id));
                        await addAppointmentToCalendar(appointment, true);
                    }
                } catch (e) {}

                if (onBookingSuccess) {
                    onBookingSuccess(appointment);
                }
            },
            onError: (error) => {
                const message = getErrorMessage(error, 'somethingWentWrong');

                notifications.error(t(message));
            },
        },
    );

    const addAppointmentToCalendar = useCallback(
        async (appointment: AppointmentModel, skipNavigation = false) => {
            const title = `BOOKR ${t('appointment')} - ${service.name} ${t('with')} ${staff.displayName} | ${
                business.name
            }`;
            const res = await addToCalendar(title, timestamp, service, staff, business, String(appointment.id));
            if (res.status === 'success') {
                notifications.success(t('addedToCalendar'));
            } else {
                notifications.error(t(res.code as TranslationKeys));
            }
            !skipNavigation && navigate('ClientApplication', { screen: 'Home' });
        },
        [business, navigate, notifications, service, staff, t, timestamp],
    );

    const addToWaitingListMutation = useMutation(
        'addToWaitingList',
        () => waitingListEndpoint.subscribeToWaitingList(staffId, current),
        {
            retry: false,
            onSuccess: (subscribed: boolean) => {
                if (subscribed) {
                    logAnalyticsEvent(AnalyticsEvent.WaitingListSubscriptions);
                    notifications.success(t('subscribedSuccessfullyToWaitingList'));
                } else {
                    notifications.error(t('couldNotSubscribeToWaitingList'));
                }
            },
            onError: (error: any) => {
                const message = getErrorMessage(error, 'somethingWentWrong');

                notifications.error(t(message));
            },
        },
    );

    const computeTimestamp = (timeslotStart: string) => {
        return Math.floor(moment.utc(current + ' ' + timeslotStart, 'YYYY-MM-DD HH:mm').valueOf() / 1000);
    };

    const handleDayPress = useCallback(
        ({ dateString }: DateData) => {
            setCurrent(dateString);
            setTimestamp(-1);
        },
        [setCurrent],
    );

    const handleHourPress = (timeslot: TimeslotDTO): void => {
        const timeslotStart = timeslot.start;
        const timestamp = computeTimestamp(timeslotStart);
        setTimestamp(timestamp);
        setTimeslot(timeslot);
    };

    const handleBookNow = useCallback(
        () =>
            bookNowMutation.mutateAsync({
                timestamp,
                staffId,
                serviceId,
                client: { uid: user?.uid } as UserModel,
                inviteClient: false,
            }),
        [bookNowMutation, serviceId, staffId, timestamp, user?.uid],
    );

    const handleAddToWaitingList = (): Promise<boolean> => addToWaitingListMutation.mutateAsync();

    const availableTimeslots = !timeslotsData
        ? []
        : convertTimesToBusinessTimezone(current, timeslotsData, business.zoneId);

    const markedDates = useMemo(() => {
        if (!datesThatCanBeBooked) {
            return {};
        }

        return Object.entries(datesThatCanBeBooked).reduce(
            (acc, [date, canBeBooked]) => ({
                ...acc,
                [date]: {
                    marked: true,
                    dotColor: canBeBooked ? theme.palette.accent.main : theme.palette.error.main,
                },
            }),
            {},
        );
    }, [datesThatCanBeBooked, theme.palette.accent.main, theme.palette.error.main]);

    const handleOnSubmit = useCallback(() => {
        if (isEditAppointment) {
            // noinspection JSIgnoredPromiseFromCall
            updateAppointmentMutation.mutateAsync();
        } else {
            handleBookNow();
        }
    }, [handleBookNow, isEditAppointment, updateAppointmentMutation]);

    return (
        <Grid flex bgColor={'backgroundPrimary'} padding={0}>
            <Screen
                disableScroll
                disablePadding
                bounces={true}
                bgColor={'backgroundPrimary'}
                stickyHeaderIndices={[0]}
                showsVerticalScrollIndicator={false}
            >
                {!hideHeader && (
                    <ScreenHeader
                        px={containerPadding}
                        leftSlot={<ScreenHeaderBackButton />}
                        caption={isEditAppointment ? t('editAppointment') : t('appointment')}
                        captionTypographyProps={{
                            fontSize: 12,
                            color: 'primary',
                            pt: 0.5,
                        }}
                    />
                )}
                <Typography variant={'title2'} fontWeight={700} pb={1.5} px={containerPadding}>
                    {t('chooseYourDate')}
                </Typography>
                <Grid flex scrollable>
                    <VStack onLayout={layout.handler}>
                        {/* seems that layout width update causes calendar not to be displayed as expected */}
                        {layout.initialized && (
                            <Calendar
                                current={current}
                                markingType={'period'}
                                markedDates={{
                                    ...markedDates,
                                    [current]: {
                                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                        // @ts-ignore
                                        ...markedDates[current],
                                        selected: true,
                                        customContainerStyle: {
                                            backgroundColor: theme.palette.typography.primary,
                                            height: 34,
                                            width: 34,
                                            borderRadius: 20,
                                        },
                                        customTextStyle: {
                                            color: theme.palette.typography.secondary,
                                        },
                                    },
                                }}
                                onDayPress={handleDayPress}
                                // calendarWidth={layout.width}
                                minDate={getMinStaffAppointmentDate(business.startingDate)}
                                maxDate={getMaxStaffAppointmentDate(service, staff)}
                            />
                        )}
                    </VStack>
                    {timeslotsData && (
                        <Grid px={containerPadding}>
                            <Typography variant={'title2'} fontWeight={700}>
                                {t('chooseYourTime')}
                            </Typography>
                            <Typography pt={1} variant={'footnote'} color={'textSecondary'}>
                                {t('timezoneMessage')}
                            </Typography>
                            {availableTimeslots.length > 0 ? (
                                <HStack pt={2} justifyContent={'center'}>
                                    {availableTimeslots.map((timeslot, index) => (
                                        <SelectableButton
                                            isSelected={computeTimestamp(timeslot.originalValue.start) === timestamp}
                                            label={timeslot.displayValue.start}
                                            onPress={(): void => handleHourPress(timeslot.originalValue)}
                                            key={index}
                                        />
                                    ))}
                                </HStack>
                            ) : (
                                <Typography
                                    variant={'subhead'}
                                    color={'textSecondary'}
                                    lineHeight={22.5}
                                    fontWeight={500}
                                    mt={2}
                                >
                                    {t('noTimeSlotsAvailable')}
                                </Typography>
                            )}
                        </Grid>
                    )}
                </Grid>
                <VStack pb={insets.bottom} mt={2} px={containerPadding}>
                    {availableTimeslots.length === 0 ? (
                        <Button
                            color={'accent'}
                            size={'large'}
                            label={t('addMeToWaitingList') as string}
                            loading={isTimeslotsLoading || addToWaitingListMutation.isLoading}
                            onPress={handleAddToWaitingList}
                        />
                    ) : (
                        <VStack>
                            {/* display how many clients already booked the timeslot */}
                            {timeslot && timeslot.groupService && (
                                <HStack
                                    borderRadius={24}
                                    alignItems={'center'}
                                    bgColor={'backgroundSecondary'}
                                    mb={2}
                                    p={1}
                                    alignSelf={'center'}
                                >
                                    <HStack
                                        borderRadius={20}
                                        bgColor={'typography.primary'}
                                        px={1}
                                        py={0.5}
                                        alignItems={'center'}
                                        justifyContent={'center'}
                                    >
                                        <Typography
                                            variant={'caption2'}
                                            fontWeight={600}
                                            color={'typography.secondary'}
                                        >
                                            {timeslot.currentBookings || 0}/{timeslot.maxBookings || 0}
                                        </Typography>
                                    </HStack>
                                    <Typography
                                        variant={'caption1'}
                                        fontWeight={600}
                                        color={'typography.primary'}
                                        ml={1}
                                    >
                                        {(timeslot.currentBookings || 0) === 0
                                            ? t('noPersonRegistered')
                                            : (timeslot.currentBookings || 0) === 1
                                            ? t('onePersonRegistered')
                                            : t('peopleRegistered')}
                                    </Typography>
                                </HStack>
                            )}
                            <Button
                                color={'accent'}
                                size={'large'}
                                label={(isEditAppointment ? t('editAppointment') : t('bookNow')) as string}
                                loading={
                                    isTimeslotsLoading ||
                                    bookNowMutation.isLoading ||
                                    updateAppointmentMutation.isLoading
                                }
                                onPress={handleOnSubmit}
                                disabled={timestamp < 0}
                            />
                        </VStack>
                    )}
                </VStack>
            </Screen>
        </Grid>
    );
}
